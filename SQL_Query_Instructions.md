# Complete SQL Query Instructions for TeleTest Django Application

## Database Configuration
- **Database Type**: PostgreSQL (primary), SQLite (development fallback)
- **Database Name**: `teletest_db`
- **Read-only User**: `tt_read_only_user` (for dashboard queries)
- **Connection**: Uses `django.db.backends.postgresql_psycopg2`

## Core Database Tables and Relationships

### 1. User Management Tables
- **`auth_user`**: Django's built-in user table
  - Primary key: `id`
  - Key fields: `email`, `first_name`, `last_name`, `is_active`, `date_joined`

- **`website_profile`**: Extended user profile information
  - Primary key: `id`
  - Foreign key: `user_id` → `auth_user.id`
  - Key fields: `profile_email`, `birthday`, `sex`, `phone`, `health_number`, `province`, `created`, `completed`

### 2. Core Business Tables

#### **`website_questionnaire`**: Main business entity for customer interactions
- Primary key: `id`
- Foreign keys:
  - `user_id` → `auth_user.id`
  - `patient_id` → `website_profile.id`
  - `doctor_id` → `website_doctorprofile.id`
  - `intake_chat_id` → `schat_room.id`
  - `lab_result_id` → `website_labresult.id`
- Key fields: `created`, `completed`, `paid`, `intake_reviewed`, `plan_started`, `sku`, `intake_kind`

#### **`website_purchase`**: Purchase transactions
- Primary key: `id`
- Foreign keys:
  - `user_id` → `auth_user.id`
  - `coupon_id` → `analytics_coupon.id`
- Key fields: `userEmail`, `purchase_date`, `amount`, `sku`, `first_name`, `last_name`, `refunded`

#### **`website_labresult`**: Lab test results
- Primary key: `id`
- Foreign keys:
  - `lab_id` → `website_lab.id`
  - `questionnaire_id` → `website_questionnaire.id` (OneToOne)
- Key fields: `created`, `completed`, `patient_confirmation`, `sent`, `visit_date`, `flagged`, `result_assays`

#### **`schat_room`**: Chat/messaging sessions
- Primary key: `id`
- Foreign keys:
  - `questionnaire_id` → `website_questionnaire.id`
- Key fields: `created`, `completed`, `kind`

### 3. Analytics Tables

#### **`analytics_streamrow`**: User activity tracking
- Primary key: `id`
- Foreign keys:
  - `user_id` → `auth_user.id`
  - `questionnaire_id` → `website_questionnaire.id`
  - `profile_id` → `website_profile.id`
- Key fields: `uuid`, `master_uuid`, `email`, `created`, `paid`, `path`, `HTTP_REFERER`, `utm_campaign`, `bounced`

#### **`analytics_keyval`**: Additional analytics data
- Primary key: `id`
- Foreign key: `uuid` → `analytics_streamrow.uuid`
- Key fields: `blob` (JSONB), `uuid`

### 4. Product and Configuration Tables

#### **`website_product`**: Available products/services
- Primary key: `id`
- Key fields: `key`, `name`, `price`, `sku`, `active`

#### **`website_lab`**: Laboratory locations
- Primary key: `id`
- Key fields: `name`, `provider`, `province`, `city`, `closed`, `time_all`, `time_blood`, `time_urine`

## Common SQL Query Patterns

### 1. Customer Journey Analysis
```sql
SELECT 
    q.id AS questionnaire_id,
    u.email,
    q.created AS q_created,
    q.completed AS q_completed,
    q.paid,
    lr.created AS lab_result_created,
    lr.completed AS lab_result_completed,
    p.purchase_date,
    p.amount
FROM website_questionnaire q
LEFT JOIN auth_user u ON q.user_id = u.id
LEFT JOIN website_labresult lr ON q.id = lr.questionnaire_id
LEFT JOIN website_purchase p ON u.email = p."userEmail"
WHERE q.created > '2023-01-01'
ORDER BY q.created DESC;
```

### 2. Conversion Rate Analysis
```sql
WITH user_stats AS (
    SELECT 
        COUNT(DISTINCT master_uuid) AS total_users,
        COUNT(DISTINCT CASE WHEN paid IS NOT NULL THEN master_uuid END) AS paid_users
    FROM analytics_streamrow
    WHERE created > '2023-01-01'
)
SELECT 
    total_users,
    paid_users,
    ROUND((paid_users::decimal / total_users) * 100, 2) AS conversion_rate
FROM user_stats;
```

### 3. Revenue Analysis
```sql
SELECT 
    DATE_TRUNC('month', purchase_date) AS month,
    COUNT(*) AS num_purchases,
    SUM(amount) AS total_revenue,
    AVG(amount) AS avg_order_value
FROM website_purchase
WHERE refunded = false
    AND purchase_date > '2023-01-01'
GROUP BY DATE_TRUNC('month', purchase_date)
ORDER BY month;
```

## Important Field Types and Conventions

### Date/Time Fields
- **`created`**: Auto-populated creation timestamp
- **`modified`**: Auto-updated modification timestamp  
- **`completed`**: Manual completion timestamp
- **`paid`**: Payment confirmation timestamp

### JSON Fields
- **`components`**: FormIO form components (JSONB)
- **`blob`**: Analytics key-value data (JSONB)
- **`post_data`**: HTTP POST data (JSONB)

### Choice Fields (use exact string values)
- **`sex`**: 'male', 'female'
- **`province`**: 'ON', 'BC', 'AB', etc.
- **`intake_kind`**: 'mail', 'chat', 'advice'

### Key Naming Conventions
- Foreign keys typically end with `_id`
- Email fields: `email`, `userEmail`, `profile_email`
- UUID fields: `uuid`, `master_uuid`
- SKU fields: `sku`, `sbx_sku`

## Performance Considerations

### Indexed Fields
- `master_uuid`, `uuid` (analytics tables)
- `email` fields
- `created`, `paid` timestamps
- `REMOTE_ADDR` (analytics)

### Large Tables (use LIMIT for testing)
- `analytics_streamrow` (millions of rows)
- `analytics_keyval` (millions of rows)
- `activity_logentry` (audit logs)

## Common Filters and Conditions

### Exclude Test/Internal Data
```sql
-- Exclude internal emails
WHERE (email IS NULL OR email NOT LIKE 'teletest.ca%')

-- Exclude empty UUIDs
AND ("analytics_streamrow"."uuid" = '') IS NOT TRUE

-- Date ranges
AND created > '2023-01-01'
```

### Window Functions for User Journey
```sql
-- First landing page per user
RANK() OVER (PARTITION BY master_uuid ORDER BY created) AS rank

-- Latest payment per user
MAX(paid) OVER (PARTITION BY master_uuid) AS max_paid
```

### JSON Queries (PostgreSQL)
```sql
-- Check if JSON contains key
WHERE blob ? 'fbclid'

-- Extract JSON values
SELECT blob->>'campaign' AS campaign_name
```

## Sample Complex Queries

### User Funnel Analysis
```sql
WITH funnel AS (
    SELECT 
        master_uuid,
        MIN(created) AS first_visit,
        MAX(CASE WHEN path LIKE '%/cart%' THEN created END) AS cart_visit,
        MAX(paid) AS payment_date
    FROM analytics_streamrow
    WHERE created > '2023-01-01'
        AND master_uuid IS NOT NULL
    GROUP BY master_uuid
)
SELECT 
    COUNT(*) AS total_visitors,
    COUNT(cart_visit) AS reached_cart,
    COUNT(payment_date) AS completed_purchase,
    ROUND(COUNT(cart_visit)::decimal / COUNT(*) * 100, 2) AS cart_rate,
    ROUND(COUNT(payment_date)::decimal / COUNT(*) * 100, 2) AS conversion_rate
FROM funnel;
```

### Lab Performance Analysis
```sql
SELECT
    l.name AS lab_name,
    l.provider,
    l.province,
    COUNT(lr.id) AS total_results,
    AVG(EXTRACT(EPOCH FROM (lr.completed - lr.created))/3600) AS avg_turnaround_hours,
    COUNT(CASE WHEN lr.flagged THEN 1 END) AS flagged_results
FROM website_lab l
LEFT JOIN website_labresult lr ON l.id = lr.lab_id
WHERE lr.created > '2023-01-01'
GROUP BY l.id, l.name, l.provider, l.province
ORDER BY total_results DESC;
```

### Campaign Attribution
```sql
SELECT
    ast.utm_campaign,
    COUNT(DISTINCT ast.master_uuid) AS unique_visitors,
    COUNT(DISTINCT CASE WHEN ast.paid IS NOT NULL THEN ast.master_uuid END) AS converters,
    SUM(p.amount) AS total_revenue
FROM analytics_streamrow ast
LEFT JOIN website_purchase p ON ast.email = p."userEmail"
    AND DATE(ast.paid) = DATE(p.purchase_date)
WHERE ast.created > '2023-01-01'
    AND ast.utm_campaign IS NOT NULL
GROUP BY ast.utm_campaign
ORDER BY total_revenue DESC NULLS LAST;
```

## Advanced Patterns

### Time-based Cohort Analysis
```sql
WITH cohorts AS (
    SELECT
        master_uuid,
        DATE_TRUNC('month', MIN(created)) AS cohort_month,
        MIN(created) AS first_visit,
        MAX(paid) AS payment_date
    FROM analytics_streamrow
    WHERE created > '2023-01-01'
    GROUP BY master_uuid
),
cohort_data AS (
    SELECT
        cohort_month,
        COUNT(*) AS cohort_size,
        COUNT(payment_date) AS converters,
        EXTRACT(MONTH FROM AGE(payment_date, cohort_month)) AS months_to_conversion
    FROM cohorts
    GROUP BY cohort_month, months_to_conversion
)
SELECT
    cohort_month,
    cohort_size,
    months_to_conversion,
    converters,
    ROUND(converters::decimal / cohort_size * 100, 2) AS conversion_rate
FROM cohort_data
WHERE months_to_conversion IS NOT NULL
ORDER BY cohort_month, months_to_conversion;
```

### Customer Lifetime Value
```sql
WITH customer_metrics AS (
    SELECT
        p."userEmail",
        COUNT(*) AS purchase_count,
        SUM(p.amount) AS total_spent,
        MIN(p.purchase_date) AS first_purchase,
        MAX(p.purchase_date) AS last_purchase,
        EXTRACT(DAYS FROM (MAX(p.purchase_date) - MIN(p.purchase_date))) AS customer_lifespan_days
    FROM website_purchase p
    WHERE p.refunded = false
    GROUP BY p."userEmail"
)
SELECT
    purchase_count,
    COUNT(*) AS customer_count,
    AVG(total_spent) AS avg_ltv,
    AVG(customer_lifespan_days) AS avg_lifespan_days
FROM customer_metrics
GROUP BY purchase_count
ORDER BY purchase_count;
```

## Error Handling and Data Quality

### Check for Data Inconsistencies
```sql
-- Find questionnaires without matching purchases
SELECT q.id, q.sku, u.email
FROM website_questionnaire q
JOIN auth_user u ON q.user_id = u.id
LEFT JOIN website_purchase p ON u.email = p."userEmail"
WHERE q.paid IS NOT NULL
    AND p.id IS NULL
    AND q.created > '2023-01-01';

-- Find orphaned lab results
SELECT lr.id, lr.created
FROM website_labresult lr
LEFT JOIN website_questionnaire q ON lr.questionnaire_id = q.id
WHERE q.id IS NULL;
```

### Handle NULL Values
```sql
-- Safe aggregations with NULL handling
SELECT
    COALESCE(utm_campaign, 'direct') AS campaign,
    COUNT(*) AS visits,
    COUNT(paid) AS conversions  -- COUNT ignores NULLs
FROM analytics_streamrow
WHERE created > '2023-01-01'
GROUP BY COALESCE(utm_campaign, 'direct');
```

## Query Optimization Tips

1. **Use appropriate date ranges** to limit large table scans
2. **Filter early** in subqueries before JOINs
3. **Use EXISTS** instead of IN for large subqueries
4. **Leverage indexes** on uuid, email, and date fields
5. **Use LIMIT** when testing queries on large tables
6. **Consider partitioning** for time-series analysis

## Common Gotchas

1. **Email field variations**: `email`, `userEmail`, `profile_email`
2. **Case sensitivity**: PostgreSQL is case-sensitive for string comparisons
3. **JSON field access**: Use `->` for JSON objects, `->>` for text values
4. **Time zones**: All timestamps are stored in UTC
5. **Soft deletes**: Some models use `active` flags instead of deletion
6. **UUID handling**: Empty strings vs NULL values in UUID fields

## Real-World Query Examples from TeleTest

### Purchase Analysis with Customer Demographics
```sql
-- Based on purch_mod.sql
WITH "DistinctCounts" AS (
    SELECT "userEmail", COUNT(DISTINCT id) AS num_purchases
    FROM website_purchase
    GROUP BY "userEmail"
)
SELECT
    p.id,
    p."userEmail",
    pr.uuid,
    pr.created::date AS date_joined,
    p.purchase_date,
    p.first_name,
    p.last_name,
    p.skus,
    MAX(ast.utm_campaign) AS utm_campaign,
    dc.num_purchases,
    p.amount,
    p.coupon_id,
    pr.sex,
    EXTRACT(YEAR FROM age(CURRENT_DATE, pr.birthday)) AS age
FROM website_purchase AS p
JOIN website_profile AS pr ON p."userEmail" = pr.profile_email
LEFT JOIN analytics_streamrow AS ast ON p."userEmail" = ast.email
    AND LEFT(p.purchase_date::text, 10) = LEFT(ast.paid::text, 10)
LEFT JOIN "DistinctCounts" AS dc ON p."userEmail" = dc."userEmail"
GROUP BY p.id, p."userEmail", pr.uuid, p.first_name, p.last_name,
    pr.created, p.purchase_date, p.amount, p.skus, dc.num_purchases,
    p.coupon_id, pr.sex, pr.birthday
ORDER BY p.purchase_date DESC;
```

### Referrer Conversion Analysis
```sql
-- Based on conversion-refs.sql
SELECT
    COUNT(DISTINCT(master_uuid)) AS num_users,
    COUNT(DISTINCT(CASE WHEN paid IS NOT NULL THEN master_uuid END)) AS num_converters,
    substring("analytics_streamrow"."HTTP_REFERER"
        from '(?:.*://)?(?:www\.)?(?:com\.)?([^/]*)?(?:(\.com|\.ca|\.gm|\.googlequicksearchbox|\.net|\.co\.uk|\.cc|\.fr|\.com\.ua))?') as ref
FROM analytics_streamrow
WHERE (master_uuid = '') IS NOT TRUE
    AND ("HTTP_REFERER" = '') IS NOT TRUE
    AND created > '2023-01-01'
    AND position('teletest' in "HTTP_REFERER") = 0
    AND position('stripe' in "HTTP_REFERER") = 0
GROUP BY ref
ORDER BY num_converters DESC;
```

### Landing Page Performance with Bounce Rate
```sql
-- Based on 0-landing-blog.sql pattern
WITH sq AS (
    SELECT
        COUNT(DISTINCT(master_uuid)) AS num_users,
        COUNT(DISTINCT(CASE WHEN max_paid IS NULL THEN master_uuid END)) AS num_not_paid,
        COUNT(DISTINCT(CASE WHEN bounced = true THEN master_uuid END)) AS num_bounced,
        (CURRENT_DATE - created::date) / 28 AS date_index,
        landing_page
    FROM (
        SELECT *,
            path AS landing_page,
            RANK() OVER (PARTITION BY master_uuid ORDER BY created) AS rank,
            MAX(paid) OVER (PARTITION BY master_uuid) AS max_paid
        FROM analytics_streamrow
        WHERE created > '2023-01-01'
            AND path IS NOT NULL
            AND ("analytics_streamrow"."uuid" = '') IS NOT TRUE
            AND (email IS NULL OR email NOT LIKE 'teletest.ca%')
    ) A
    WHERE rank = 1
    GROUP BY landing_page, date_index
    ORDER BY num_users DESC
)
SELECT
    date_index,
    TO_CHAR(CURRENT_DATE - INTERVAL '1 day' * (28 * date_index), 'YYYY-MM-DD') AS end_date,
    num_users,
    ROUND((num_bounced::decimal / num_users) * 100, 2) || '%' AS bounce_rate,
    num_users - num_bounced AS not_bounced,
    CEIL((1.0 - (num_not_paid::decimal/num_users))*100.0) || '%' AS conversion_rate,
    num_users - num_not_paid AS num_paid,
    landing_page
FROM sq
WHERE landing_page LIKE '%blog%'
    AND date_index BETWEEN 0 AND 2
ORDER BY landing_page, date_index, num_users DESC;
```

## Key SQL Functions and Operators Used

### String Functions
- `substring()` with regex for domain extraction
- `position()` for string searching
- `LEFT()` for date comparison
- `LIKE` with wildcards for pattern matching

### Date/Time Functions
- `EXTRACT()` for age calculation
- `DATE_TRUNC()` for time grouping
- `TO_CHAR()` for date formatting
- `INTERVAL` for date arithmetic

### Window Functions
- `RANK() OVER (PARTITION BY ... ORDER BY ...)` for user journey analysis
- `MAX() OVER (PARTITION BY ...)` for latest values per group

### Conditional Aggregation
- `COUNT(CASE WHEN condition THEN 1 END)` for conditional counting
- `COUNT(DISTINCT CASE WHEN ... THEN field END)` for unique conditional counts

### PostgreSQL-Specific Features
- `::date`, `::text` for type casting
- `||` for string concatenation
- `?` operator for JSON key existence
- Regex support in `substring()`

This comprehensive guide provides all the essential information needed for AI to write effective, optimized SQL queries against the TeleTest database, including real-world examples and patterns used in production.
