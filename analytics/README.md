# TeleTest Analytics SQL Queries

This folder contains specialized SQL queries for business analytics and reporting.

## Files

### `glp1_churn_analysis.sql`
**Purpose**: Comprehensive cohort analysis for GLP-1 medication patient retention and churn tracking.

**Business Use Case**: 
- Track patient retention at 3, 6, 9, and 12-month intervals
- Identify churn patterns for intervention opportunities
- Analyze cohort performance over time
- Support customer success and retention initiatives

**Key Metrics Provided**:
- Cohort size and demographics (age, gender breakdown)
- Retention rates and counts at each time interval
- Churn rates and counts at each time interval
- Statistical significance filtering (minimum cohort size)

**Configuration Required**:
1. Update SKU arrays in the `params` CTE to match your actual product SKUs
2. Adjust date range filter for analysis period
3. Modify window_days parameter based on billing cycle (default: 45 days)

**Sample Output**:
```
cohort_month | cohort_size | retained_3m | retained_3m_pct | churn_3m | churn_3m_pct
2024-01-01   | 150         | 120         | 80.0            | 30       | 20.0
2024-02-01   | 175         | 140         | 80.0            | 35       | 20.0
```

**Performance Notes**:
- Uses efficient EXISTS clauses for retention checks
- Includes proper indexing recommendations
- Filters small cohorts for statistical significance
- Optimized for large datasets with date range filtering

**Dependencies**:
- `website_purchase` table
- `website_profile` table
- Requires PostgreSQL for interval arithmetic and array operations

## Usage Guidelines

### Running Queries
1. Always test queries with LIMIT clauses on large datasets first
2. Verify SKU values match your actual product catalog
3. Adjust date ranges to avoid unnecessary data processing
4. Consider creating materialized views for frequently-run analyses

### Performance Optimization
- Ensure indexes exist on:
  - `website_purchase.userEmail`
  - `website_purchase.purchase_date`
  - `website_purchase.sku`
  - `website_profile.profile_email`

### Data Quality Checks
Before running analysis queries, verify:
- SKU naming conventions are consistent
- Purchase dates are properly formatted
- Refund status is accurately recorded
- Email addresses are properly normalized

## Adding New Analytics Queries

When adding new SQL files to this folder:

1. **Use descriptive filenames** that indicate the business purpose
2. **Include comprehensive comments** explaining business logic
3. **Add configuration sections** for easy customization
4. **Document dependencies** and required indexes
5. **Update this README** with new file descriptions

### Template Structure
```sql
-- =====================================================================================
-- [Query Title] for TeleTest
-- =====================================================================================
-- Purpose: [Business purpose and use case]
-- Author: [Your name]
-- Date: [Creation date]
-- 
-- Business Logic:
-- - [Key assumption 1]
-- - [Key assumption 2]
-- 
-- Configuration:
-- - [Parameter 1]: [Description]
-- - [Parameter 2]: [Description]
-- =====================================================================================

WITH params AS (
  SELECT 
    -- Configuration parameters here
),

-- Step-by-step CTEs with clear business logic
main_analysis AS (
  -- Main query logic
)

-- Final output with clear column names and documentation
SELECT 
  -- Results
FROM main_analysis
ORDER BY relevant_field;

-- =====================================================================================
-- USAGE NOTES:
-- [Detailed usage instructions]
-- =====================================================================================
```

## Best Practices

### SQL Style Guide
- Use uppercase for SQL keywords (SELECT, FROM, WHERE, etc.)
- Use meaningful CTE names that describe business concepts
- Include comprehensive comments for complex business logic
- Use consistent indentation and formatting
- Add NULLIF() protection for division operations

### Business Logic Documentation
- Clearly state assumptions about data quality
- Document any business rules or grace periods
- Explain calculation methodologies
- Include sample outputs for validation

### Performance Considerations
- Always include appropriate WHERE clauses for date filtering
- Use EXISTS instead of IN for large subqueries
- Consider query execution plans for optimization
- Document recommended indexes for production use

## Support

For questions about these analytics queries:
1. Review the inline documentation in each SQL file
2. Check the TeleTest database schema documentation
3. Validate SKU values and table relationships
4. Test queries on sample data before production use

## Future Enhancements

Potential additions to this analytics suite:
- Revenue cohort analysis
- Customer lifetime value calculations
- Funnel analysis for different product lines
- Seasonal trend analysis
- Geographic performance breakdowns
- A/B testing result analysis
