-- GLP-1 Patient Churn Analysis for TeleTest (Dashboard Version)
-- Purpose: Track retention and churn of GLP-1 patients at 3, 6, 9, and 12 month intervals
-- Configuration: Update SKU arrays below to match your actual product SKUs

WITH params AS (
  SELECT 
    ARRAY['semaglutide_init', 'tirzepatide_init', 'glp1_initiation']::text[] AS init_skus,
    ARRAY['semaglutide_renewal', 'tirzepatide_renewal', 'glp1_renewal']::text[] AS renewal_skus,
    45::int AS window_days
),

first_inits AS (
  SELECT
    p."userEmail" AS patient_email,
    pr.id AS patient_id,
    pr.firstname,
    pr.lastname,
    pr.sex,
    pr.birthday,
    MIN(p.purchase_date) FILTER (
      WHERE p.sku = ANY(params.init_skus) 
      AND p.refunded = false
    ) AS first_init_at
  FROM website_purchase p
  CROSS JOIN params
  LEFT JOIN website_profile pr ON p."userEmail" = pr.profile_email
  WHERE p.purchase_date >= '2023-01-01'
  GROUP BY p."userEmail", pr.id, pr.firstname, pr.lastname, pr.sex, pr.birthday
),

cohort AS (
  SELECT 
    patient_email,
    patient_id,
    firstname,
    lastname,
    sex,
    EXTRACT(YEAR FROM AGE(CURRENT_DATE, birthday)) AS age,
    first_init_at,
    DATE_TRUNC('month', first_init_at)::date AS cohort_month
  FROM first_inits
  WHERE first_init_at IS NOT NULL
),

renewals AS (
  SELECT 
    p."userEmail" AS patient_email,
    p.purchase_date AS renewal_at,
    p.amount AS renewal_amount,
    p.sku AS renewal_sku
  FROM website_purchase p
  CROSS JOIN params
  JOIN cohort c ON c.patient_email = p."userEmail"
  WHERE p.sku = ANY(params.renewal_skus)
    AND p.refunded = false
    AND p.purchase_date >= c.first_init_at
),

per_patient_flags AS (
  SELECT
    c.patient_email,
    c.patient_id,
    c.firstname,
    c.lastname,
    c.sex,
    c.age,
    c.cohort_month,
    c.first_init_at,
    
    (c.first_init_at + INTERVAL '3 months')  AS m3_anchor,
    (c.first_init_at + INTERVAL '6 months')  AS m6_anchor,
    (c.first_init_at + INTERVAL '9 months')  AS m9_anchor,
    (c.first_init_at + INTERVAL '12 months') AS m12_anchor,

    CASE WHEN EXISTS (
      SELECT 1 FROM renewals r, params p
      WHERE r.patient_email = c.patient_email
        AND r.renewal_at >= (c.first_init_at + INTERVAL '3 months')
        AND r.renewal_at < (c.first_init_at + INTERVAL '3 months') + (p.window_days || ' days')::interval
    ) THEN 1 ELSE 0 END AS retained_3m,
    
    CASE WHEN EXISTS (
      SELECT 1 FROM renewals r, params p
      WHERE r.patient_email = c.patient_email
        AND r.renewal_at >= (c.first_init_at + INTERVAL '6 months')
        AND r.renewal_at < (c.first_init_at + INTERVAL '6 months') + (p.window_days || ' days')::interval
    ) THEN 1 ELSE 0 END AS retained_6m,
    
    CASE WHEN EXISTS (
      SELECT 1 FROM renewals r, params p
      WHERE r.patient_email = c.patient_email
        AND r.renewal_at >= (c.first_init_at + INTERVAL '9 months')
        AND r.renewal_at < (c.first_init_at + INTERVAL '9 months') + (p.window_days || ' days')::interval
    ) THEN 1 ELSE 0 END AS retained_9m,
    
    CASE WHEN EXISTS (
      SELECT 1 FROM renewals r, params p
      WHERE r.patient_email = c.patient_email
        AND r.renewal_at >= (c.first_init_at + INTERVAL '12 months')
        AND r.renewal_at < (c.first_init_at + INTERVAL '12 months') + (p.window_days || ' days')::interval
    ) THEN 1 ELSE 0 END AS retained_12m
  FROM cohort c
),

cohort_rollup AS (
  SELECT
    cohort_month,
    COUNT(*)::int AS cohort_size,
    
    SUM(retained_3m)::int  AS retained_3m,
    SUM(retained_6m)::int  AS retained_6m,
    SUM(retained_9m)::int  AS retained_9m,
    SUM(retained_12m)::int AS retained_12m,
    
    (COUNT(*) - SUM(retained_3m))::int  AS churn_3m,
    (COUNT(*) - SUM(retained_6m))::int  AS churn_6m,
    (COUNT(*) - SUM(retained_9m))::int  AS churn_9m,
    (COUNT(*) - SUM(retained_12m))::int AS churn_12m,
    
    COUNT(*) FILTER (WHERE sex = 'female')::int AS female_count,
    COUNT(*) FILTER (WHERE sex = 'male')::int AS male_count,
    ROUND(AVG(age), 1) AS avg_age
  FROM per_patient_flags
  GROUP BY cohort_month
)

SELECT
  cohort_month,
  cohort_size,
  female_count,
  male_count,
  avg_age,
  
  retained_3m,
  ROUND(100.0 * retained_3m / NULLIF(cohort_size, 0), 1) AS retained_3m_pct,
  churn_3m,
  ROUND(100.0 * churn_3m / NULLIF(cohort_size, 0), 1) AS churn_3m_pct,
  
  retained_6m,
  ROUND(100.0 * retained_6m / NULLIF(cohort_size, 0), 1) AS retained_6m_pct,
  churn_6m,
  ROUND(100.0 * churn_6m / NULLIF(cohort_size, 0), 1) AS churn_6m_pct,
  
  retained_9m,
  ROUND(100.0 * retained_9m / NULLIF(cohort_size, 0), 1) AS retained_9m_pct,
  churn_9m,
  ROUND(100.0 * churn_9m / NULLIF(cohort_size, 0), 1) AS churn_9m_pct,
  
  retained_12m,
  ROUND(100.0 * retained_12m / NULLIF(cohort_size, 0), 1) AS retained_12m_pct,
  churn_12m,
  ROUND(100.0 * churn_12m / NULLIF(cohort_size, 0), 1) AS churn_12m_pct
  
FROM cohort_rollup
WHERE cohort_size >= 10
ORDER BY cohort_month DESC
